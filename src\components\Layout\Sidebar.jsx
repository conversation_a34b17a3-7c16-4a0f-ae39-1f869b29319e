import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  UserGroupIcon,
  UsersIcon,
  BriefcaseIcon,
  ShoppingBagIcon,
  DocumentTextIcon,
  UserIcon,
  CogIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Vendors', href: '/vendors', icon: UserGroupIcon },
  { name: 'Clients', href: '/clients', icon: UsersIcon },
  { name: 'Type of Work', href: '/type-of-work', icon: BriefcaseIcon },
  { name: 'Orders', href: '/orders', icon: ShoppingBagIcon },
  { name: 'Audit', href: '/audit', icon: DocumentTextIcon },
  { name: 'Sub-admins', href: '/sub-admins', icon: UserIcon },
  { name: 'Settings', href: '/settings', icon: CogIcon },
];

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();

  return (
    <>
      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64 xl:w-72">
          <div className="flex flex-col flex-grow bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 border-r border-gray-200/80 pt-6 pb-4 overflow-y-auto shadow-xl backdrop-blur-sm">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0 px-4 lg:px-6 mb-8 lg:mb-10">
              <div className="w-full bg-white/60 backdrop-blur-sm rounded-2xl p-3 lg:p-4 shadow-lg border border-white/20">
                <img
                  src="/innoventorysologo.png"
                  alt="Innoventory Logo"
                  className="h-14 lg:h-16 xl:h-18 w-full object-contain"
                />
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-3 lg:px-4 space-y-2 lg:space-y-3">
              {navigation.map((item, index) => {
                const isActive = location.pathname === item.href ||
                  (item.href === '/dashboard' && location.pathname === '/') ||
                  (item.href !== '/dashboard' && location.pathname.startsWith(item.href + '/'));
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group relative flex items-center px-4 lg:px-5 py-3 lg:py-4 text-sm lg:text-base font-medium rounded-xl lg:rounded-2xl transition-all duration-300 transform hover:scale-[1.02] ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-xl shadow-blue-500/30 border border-blue-400/20'
                        : 'text-gray-700 hover:bg-gradient-to-r hover:from-blue-50/80 hover:via-indigo-50/60 hover:to-purple-50/40 hover:text-blue-700 hover:shadow-lg backdrop-blur-sm'
                    }`}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <item.icon className={`mr-3 lg:mr-4 h-5 w-5 lg:h-6 lg:w-6 transition-all duration-300 ${
                      isActive ? 'text-white drop-shadow-sm' : 'text-gray-500 group-hover:text-blue-600 group-hover:scale-110'
                    }`} />
                    <span className="transition-all duration-300 font-medium">{item.name}</span>

                    {/* Active indicator */}
                    {isActive && (
                      <div className="absolute right-3 lg:right-4 w-2 h-2 lg:w-3 lg:h-3 bg-white rounded-full opacity-90 shadow-sm animate-pulse"></div>
                    )}

                    {/* Hover effect */}
                    <div className={`absolute inset-0 rounded-xl lg:rounded-2xl transition-all duration-300 ${
                      !isActive ? 'opacity-0 group-hover:opacity-100 bg-gradient-to-r from-blue-500/10 via-indigo-500/8 to-purple-500/6' : ''
                    }`}></div>
                  </Link>
                );
              })}
            </nav>

            {/* Footer */}
            <div className="px-4 lg:px-6 py-3 lg:py-4 border-t border-gray-200/60 mt-4 bg-gradient-to-r from-gray-50/50 to-white/30 backdrop-blur-sm">
              <div className="flex items-center text-xs lg:text-sm text-gray-600 font-medium">
                <div className="w-2 h-2 lg:w-3 lg:h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full mr-2 lg:mr-3 animate-pulse shadow-sm"></div>
                <span className="bg-gradient-to-r from-gray-700 to-gray-600 bg-clip-text text-transparent">System Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile sidebar */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 sm:w-72 bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30 backdrop-blur-xl border-r border-gray-200/50 shadow-2xl transform transition-all duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Header with close button */}
          <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200/60 bg-white/40 backdrop-blur-sm">
            <div className="flex items-center flex-1 mr-4">
              <div className="w-full bg-white/60 backdrop-blur-sm rounded-xl p-2 sm:p-3 shadow-lg border border-white/20">
                <img
                  src="/innoventorysologo.png"
                  alt="Innoventory Logo"
                  className="h-10 sm:h-12 w-full object-contain"
                />
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 sm:p-3 rounded-xl text-gray-400 hover:text-gray-600 hover:bg-white/60 backdrop-blur-sm transition-all duration-200 hover:scale-105 shadow-sm"
            >
              <XMarkIcon className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-3 sm:px-4 py-4 sm:py-6 space-y-2 sm:space-y-3">
            {navigation.map((item, index) => {
              const isActive = location.pathname === item.href ||
                (item.href === '/dashboard' && location.pathname === '/') ||
                (item.href !== '/dashboard' && location.pathname.startsWith(item.href + '/'));
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`group relative flex items-center px-4 sm:px-5 py-3 sm:py-4 text-sm sm:text-base font-medium rounded-xl sm:rounded-2xl transition-all duration-300 transform hover:scale-[1.02] ${
                    isActive
                      ? 'bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-xl shadow-blue-500/30 border border-blue-400/20'
                      : 'text-gray-700 hover:bg-gradient-to-r hover:from-blue-50/80 hover:via-indigo-50/60 hover:to-purple-50/40 hover:text-blue-700 hover:shadow-lg backdrop-blur-sm'
                  }`}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <item.icon className={`mr-3 sm:mr-4 h-5 w-5 sm:h-6 sm:w-6 transition-all duration-300 ${
                    isActive ? 'text-white drop-shadow-sm' : 'text-gray-500 group-hover:text-blue-600 group-hover:scale-110'
                  }`} />
                  <span className="transition-all duration-300 font-medium">{item.name}</span>
                  {isActive && (
                    <div className="absolute right-3 sm:right-4 w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full opacity-90 shadow-sm animate-pulse"></div>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;

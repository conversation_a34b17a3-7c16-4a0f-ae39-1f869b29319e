import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

dotenv.config();
const sql = neon(process.env.VITE_DATABASE_URL);

async function testDatabaseSave() {
  try {
    console.log('🔄 Testing database save operations...');
    
    // Test vendors table
    console.log('\n📊 Testing vendors...');
    const vendorsCount = await sql`SELECT COUNT(*) as count FROM vendors`;
    console.log('Vendors in database:', vendorsCount[0].count);
    
    if (vendorsCount[0].count > 0) {
      const sampleVendor = await sql`SELECT company_name, onboarding_date, status FROM vendors LIMIT 1`;
      console.log('Sample vendor:', sampleVendor[0]);
    }
    
    // Test clients table
    console.log('\n📊 Testing clients...');
    const clientsCount = await sql`SELECT COUNT(*) as count FROM clients`;
    console.log('Clients in database:', clientsCount[0].count);
    
    if (clientsCount[0].count > 0) {
      const sampleClient = await sql`SELECT company_name, onboarding_date, status FROM clients LIMIT 1`;
      console.log('Sample client:', sampleClient[0]);
    }
    
    // Test sub_admins table
    console.log('\n📊 Testing sub-admins...');
    const subAdminsCount = await sql`SELECT COUNT(*) as count FROM sub_admins`;
    console.log('Sub-admins in database:', subAdminsCount[0].count);
    
    // Test orders table
    console.log('\n📊 Testing orders...');
    const ordersCount = await sql`SELECT COUNT(*) as count FROM orders`;
    console.log('Orders in database:', ordersCount[0].count);
    
    // Test type_of_work table
    console.log('\n📊 Testing type of work...');
    const typeOfWorkCount = await sql`SELECT COUNT(*) as count FROM type_of_work`;
    console.log('Type of work in database:', typeOfWorkCount[0].count);
    
    console.log('\n✅ Database test completed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

testDatabaseSave();
